"use client";

import { useState } from "react";
import { <PERSON> } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

interface PatentCreationClientProps {
  locale: string;
}

export default function PatentCreationClient({ locale }: PatentCreationClientProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState("");

  const sidebarItems = [
    {
      title: locale === "zh" ? "仪表盘" : "Dashboard",
      url: "/start",
      icon: "RiDashboardLine",
      color: "bg-blue-500"
    },
    {
      title: locale === "zh" ? "专利搜索" : "Patent Search",
      url: "/patent-search",
      icon: "RiSearchLine",
      color: "bg-green-500"
    },
    {
      title: locale === "zh" ? "专利分析" : "Patent Analysis",
      url: "/patent-analysis",
      icon: "RiBarChartLine",
      color: "bg-purple-500"
    },
    {
      title: locale === "zh" ? "文档管理" : "Document Management",
      url: "/documents",
      icon: "RiFileTextLine",
      color: "bg-orange-500"
    },
    {
      title: locale === "zh" ? "专利创作" : "Patent Creation",
      url: "/patent-creation",
      icon: "RiEditLine",
      color: "bg-pink-500",
      active: true
    },
    {
      title: locale === "zh" ? "设置" : "Settings",
      url: "/settings",
      icon: "RiSettingsLine",
      color: "bg-gray-500"
    }
  ];

  const steps = [
    {
      id: 1,
      title: locale === "zh" ? "基本信息" : "Basic Info",
      description: locale === "zh" ? "填写专利基本信息" : "Fill in basic patent information"
    },
    {
      id: 2,
      title: locale === "zh" ? "技术描述" : "Technical Description",
      description: locale === "zh" ? "详细描述技术方案" : "Detailed technical solution description"
    },
    {
      id: 3,
      title: locale === "zh" ? "AI 生成" : "AI Generation",
      description: locale === "zh" ? "AI 智能生成专利文档" : "AI intelligent patent document generation"
    },
    {
      id: 4,
      title: locale === "zh" ? "审核编辑" : "Review & Edit",
      description: locale === "zh" ? "审核和编辑生成内容" : "Review and edit generated content"
    }
  ];

  const handleGenerate = async () => {
    setIsGenerating(true);
    // 模拟AI生成过程
    setTimeout(() => {
      setGeneratedContent(`
# ${locale === "zh" ? "发明专利申请书" : "Patent Application"}

## ${locale === "zh" ? "技术领域" : "Technical Field"}
${locale === "zh"
  ? "本发明涉及人工智能技术领域，特别是涉及一种基于深度学习的智能专利分析系统及方法。"
  : "The present invention relates to the field of artificial intelligence technology, and particularly to an intelligent patent analysis system and method based on deep learning."
}

## ${locale === "zh" ? "背景技术" : "Background Art"}
${locale === "zh"
  ? "随着科技的快速发展，专利申请数量呈指数级增长。传统的专利分析方法效率低下，难以满足现代企业的需求..."
  : "With the rapid development of technology, the number of patent applications has grown exponentially. Traditional patent analysis methods are inefficient and difficult to meet the needs of modern enterprises..."
}

## ${locale === "zh" ? "发明内容" : "Summary of the Invention"}
${locale === "zh"
  ? "本发明的目的是提供一种基于深度学习的智能专利分析系统，能够自动分析专利文档，提取关键技术特征，并生成分析报告。"
  : "The purpose of the present invention is to provide an intelligent patent analysis system based on deep learning that can automatically analyze patent documents, extract key technical features, and generate analysis reports."
}

## ${locale === "zh" ? "具体实施方式" : "Detailed Description"}
${locale === "zh"
  ? "如图1所示，本发明的智能专利分析系统包括数据采集模块、预处理模块、深度学习分析模块和结果输出模块..."
  : "As shown in Figure 1, the intelligent patent analysis system of the present invention includes a data collection module, a preprocessing module, a deep learning analysis module, and a result output module..."
}
      `);
      setIsGenerating(false);
      setCurrentStep(4);
    }, 3000);
  };

  return (
    <div className="flex min-h-screen w-full">
      {/* 侧边栏 */}
      <div className="w-16 bg-sidebar border-r border-border flex flex-col items-center py-4 space-y-2">
        {/* Logo */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/start">
                <div className="flex size-10 items-center justify-center rounded-lg bg-primary text-primary-foreground mb-4 cursor-pointer hover:scale-105 transition-transform">
                  <Icon name="RiFileTextLine" className="size-5" />
                </div>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p className="font-medium">
                {locale === "zh" ? "专利助手" : "Patent Assistant"}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Separator className="w-8" />

        {/* 导航菜单 */}
        <div className="flex flex-col space-y-2 flex-1">
          {sidebarItems.map((item, index) => (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={item.url}
                    className={`group flex size-10 items-center justify-center rounded-lg transition-colors relative ${
                      item.active ? 'bg-accent' : 'hover:bg-accent'
                    }`}
                  >
                    <div className={`flex size-8 items-center justify-center rounded-md ${item.color} text-white group-hover:scale-110 transition-transform shadow-sm ${
                      item.active ? 'scale-110' : ''
                    }`}>
                      <Icon name={item.icon} className="size-4" />
                    </div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="font-medium">{item.title}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>

        {/* 底部工具 */}
        <div className="flex flex-col space-y-2 mt-auto">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="size-10">
                  <Icon name="RiTranslate2" className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{locale === "zh" ? "语言设置" : "Language"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="size-10">
                  <Icon name="RiMoonLine" className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{locale === "zh" ? "主题切换" : "Theme"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部栏 */}
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <div className="flex items-center gap-2">
            <Link href="/start">
              <Button variant="ghost" size="icon">
                <Icon name="RiArrowLeftLine" className="size-4" />
              </Button>
            </Link>
            <Separator orientation="vertical" className="h-4" />
          </div>
          <div className="flex-1">
            <h1 className="text-lg font-semibold">
              {locale === "zh" ? "专利创作" : "Patent Creation"}
            </h1>
            <p className="text-sm text-muted-foreground">
              {locale === "zh"
                ? "AI 智能辅助专利文档创作"
                : "AI-powered intelligent patent document creation"
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Icon name="RiSaveLine" className="size-4 mr-2" />
              {locale === "zh" ? "保存草稿" : "Save Draft"}
            </Button>
            <Button size="sm">
              <Icon name="RiDownloadLine" className="size-4 mr-2" />
              {locale === "zh" ? "导出" : "Export"}
            </Button>
          </div>
        </header>

        <div className="flex flex-1">
          {/* 步骤导航 */}
          <div className="w-64 border-r bg-muted/30 p-4">
            <h3 className="font-semibold mb-4">
              {locale === "zh" ? "创作步骤" : "Creation Steps"}
            </h3>
            <div className="space-y-2">
              {steps.map((step) => (
                <div
                  key={step.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    currentStep === step.id
                      ? 'bg-primary text-primary-foreground'
                      : currentStep > step.id
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-background hover:bg-muted'
                  }`}
                  onClick={() => setCurrentStep(step.id)}
                >
                  <div className="flex items-center gap-2">
                    <div className={`flex size-6 items-center justify-center rounded-full text-xs font-semibold ${
                      currentStep === step.id
                        ? 'bg-primary-foreground text-primary'
                        : currentStep > step.id
                        ? 'bg-green-600 text-white'
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {currentStep > step.id ? (
                        <Icon name="RiCheckLine" className="size-3" />
                      ) : (
                        step.id
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{step.title}</p>
                      <p className="text-xs opacity-80">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 进度条 */}
            <div className="mt-6">
              <div className="flex justify-between text-sm mb-2">
                <span>{locale === "zh" ? "完成进度" : "Progress"}</span>
                <span>{Math.round((currentStep / steps.length) * 100)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${(currentStep / steps.length) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="flex-1 p-6">
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Icon name="RiInformationLine" className="size-5 text-primary" />
                    {locale === "zh" ? "基本信息" : "Basic Information"}
                  </CardTitle>
                  <CardDescription>
                    {locale === "zh" ? "请填写专利的基本信息" : "Please fill in the basic information of the patent"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">{locale === "zh" ? "专利标题" : "Patent Title"}</Label>
                      <Input id="title" placeholder={locale === "zh" ? "请输入专利标题" : "Enter patent title"} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type">{locale === "zh" ? "专利类型" : "Patent Type"}</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder={locale === "zh" ? "选择专利类型" : "Select patent type"} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="invention">{locale === "zh" ? "发明专利" : "Invention Patent"}</SelectItem>
                          <SelectItem value="utility">{locale === "zh" ? "实用新型" : "Utility Model"}</SelectItem>
                          <SelectItem value="design">{locale === "zh" ? "外观设计" : "Design Patent"}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="field">{locale === "zh" ? "技术领域" : "Technical Field"}</Label>
                    <Input id="field" placeholder={locale === "zh" ? "请输入技术领域" : "Enter technical field"} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="inventors">{locale === "zh" ? "发明人" : "Inventors"}</Label>
                    <Input id="inventors" placeholder={locale === "zh" ? "请输入发明人姓名" : "Enter inventor names"} />
                  </div>
                  <div className="flex justify-end">
                    <Button onClick={() => setCurrentStep(2)}>
                      {locale === "zh" ? "下一步" : "Next"}
                      <Icon name="RiArrowRightLine" className="size-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Icon name="RiFileTextLine" className="size-5 text-primary" />
                    {locale === "zh" ? "技术描述" : "Technical Description"}
                  </CardTitle>
                  <CardDescription>
                    {locale === "zh" ? "详细描述您的技术方案和创新点" : "Describe your technical solution and innovations in detail"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="background">{locale === "zh" ? "背景技术" : "Background Technology"}</Label>
                    <Textarea
                      id="background"
                      placeholder={locale === "zh" ? "描述现有技术的不足和问题..." : "Describe the shortcomings and problems of existing technology..."}
                      className="min-h-[100px]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="solution">{locale === "zh" ? "技术方案" : "Technical Solution"}</Label>
                    <Textarea
                      id="solution"
                      placeholder={locale === "zh" ? "详细描述您的技术方案..." : "Describe your technical solution in detail..."}
                      className="min-h-[150px]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="advantages">{locale === "zh" ? "技术优势" : "Technical Advantages"}</Label>
                    <Textarea
                      id="advantages"
                      placeholder={locale === "zh" ? "说明您的方案相比现有技术的优势..." : "Explain the advantages of your solution over existing technology..."}
                      className="min-h-[100px]"
                    />
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>
                      <Icon name="RiArrowLeftLine" className="size-4 mr-2" />
                      {locale === "zh" ? "上一步" : "Previous"}
                    </Button>
                    <Button onClick={() => setCurrentStep(3)}>
                      {locale === "zh" ? "下一步" : "Next"}
                      <Icon name="RiArrowRightLine" className="size-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Icon name="RiRobot2Line" className="size-5 text-primary" />
                    {locale === "zh" ? "AI 智能生成" : "AI Generation"}
                  </CardTitle>
                  <CardDescription>
                    {locale === "zh" ? "AI 将根据您提供的信息生成专业的专利文档" : "AI will generate professional patent documents based on the information you provided"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {!isGenerating && !generatedContent && (
                    <div className="text-center py-12">
                      <div className="flex size-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4">
                        <Icon name="RiMagicLine" className="size-8 text-primary" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2">
                        {locale === "zh" ? "准备生成专利文档" : "Ready to Generate Patent Document"}
                      </h3>
                      <p className="text-muted-foreground mb-6">
                        {locale === "zh"
                          ? "点击下方按钮，AI 将为您生成专业的专利申请文档"
                          : "Click the button below, AI will generate professional patent application documents for you"
                        }
                      </p>
                      <Button size="lg" onClick={handleGenerate}>
                        <Icon name="RiSparklingLine" className="size-4 mr-2" />
                        {locale === "zh" ? "开始生成" : "Start Generation"}
                      </Button>
                    </div>
                  )}

                  {isGenerating && (
                    <div className="text-center py-12">
                      <div className="flex size-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4 animate-pulse">
                        <Icon name="RiLoader4Line" className="size-8 text-primary animate-spin" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2">
                        {locale === "zh" ? "AI 正在生成中..." : "AI is Generating..."}
                      </h3>
                      <p className="text-muted-foreground mb-6">
                        {locale === "zh"
                          ? "请稍候，AI 正在分析您的技术方案并生成专利文档"
                          : "Please wait, AI is analyzing your technical solution and generating patent documents"
                        }
                      </p>
                      <div className="w-64 mx-auto bg-muted rounded-full h-2">
                        <div className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out animate-pulse" style={{ width: '66%' }}></div>
                      </div>
                    </div>
                  )}

                  {generatedContent && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-green-600">
                          {locale === "zh" ? "生成完成！" : "Generation Complete!"}
                        </h3>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {locale === "zh" ? "AI 生成" : "AI Generated"}
                        </Badge>
                      </div>
                      <div className="bg-muted/30 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm">{generatedContent}</pre>
                      </div>
                      <div className="flex justify-between">
                        <Button variant="outline" onClick={() => setCurrentStep(2)}>
                          <Icon name="RiArrowLeftLine" className="size-4 mr-2" />
                          {locale === "zh" ? "重新生成" : "Regenerate"}
                        </Button>
                        <Button onClick={() => setCurrentStep(4)}>
                          {locale === "zh" ? "继续编辑" : "Continue Editing"}
                          <Icon name="RiArrowRightLine" className="size-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Icon name="RiEditLine" className="size-5 text-primary" />
                    {locale === "zh" ? "审核编辑" : "Review & Edit"}
                  </CardTitle>
                  <CardDescription>
                    {locale === "zh" ? "审核和编辑 AI 生成的专利文档" : "Review and edit the AI-generated patent document"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="edit" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="edit">{locale === "zh" ? "编辑模式" : "Edit Mode"}</TabsTrigger>
                      <TabsTrigger value="preview">{locale === "zh" ? "预览模式" : "Preview Mode"}</TabsTrigger>
                    </TabsList>
                    <TabsContent value="edit" className="space-y-4">
                      <Textarea
                        value={generatedContent}
                        onChange={(e) => setGeneratedContent(e.target.value)}
                        className="min-h-[400px] font-mono text-sm"
                      />
                      <div className="flex justify-between">
                        <Button variant="outline" onClick={() => setCurrentStep(3)}>
                          <Icon name="RiArrowLeftLine" className="size-4 mr-2" />
                          {locale === "zh" ? "返回生成" : "Back to Generation"}
                        </Button>
                        <div className="flex gap-2">
                          <Button variant="outline">
                            <Icon name="RiSaveLine" className="size-4 mr-2" />
                            {locale === "zh" ? "保存" : "Save"}
                          </Button>
                          <Button>
                            <Icon name="RiDownloadLine" className="size-4 mr-2" />
                            {locale === "zh" ? "导出文档" : "Export Document"}
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent value="preview" className="space-y-4">
                      <div className="bg-white border rounded-lg p-6 min-h-[400px] shadow-sm">
                        <div className="prose prose-sm max-w-none">
                          <pre className="whitespace-pre-wrap">{generatedContent}</pre>
                        </div>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline">
                          <Icon name="RiPrinterLine" className="size-4 mr-2" />
                          {locale === "zh" ? "打印" : "Print"}
                        </Button>
                        <Button>
                          <Icon name="RiShareLine" className="size-4 mr-2" />
                          {locale === "zh" ? "分享" : "Share"}
                        </Button>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
