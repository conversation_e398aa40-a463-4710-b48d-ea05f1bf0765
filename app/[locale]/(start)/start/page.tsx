"use client";

import { <PERSON> } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface StartPageClientProps {
  locale: string;
}

function StartPageClient({ locale }: StartPageClientProps) {
  const sidebarItems = [
    {
      title: locale === "zh" ? "仪表盘" : "Dashboard",
      url: "/dashboard",
      icon: "RiDashboardLine",
      description: locale === "zh" ? "查看项目概览和统计数据" : "View project overview and statistics",
      color: "bg-blue-500"
    },
    {
      title: locale === "zh" ? "专利搜索" : "Patent Search",
      url: "/patent-search",
      icon: "RiSearchLine",
      description: locale === "zh" ? "搜索和分析专利信息" : "Search and analyze patent information",
      color: "bg-green-500"
    },
    {
      title: locale === "zh" ? "专利分析" : "Patent Analysis",
      url: "/patent-analysis",
      icon: "RiBarChartLine",
      description: locale === "zh" ? "深度分析专利数据" : "Deep analysis of patent data",
      color: "bg-purple-500"
    },
    {
      title: locale === "zh" ? "文档管理" : "Document Management",
      url: "/documents",
      icon: "RiFileTextLine",
      description: locale === "zh" ? "管理专利文档和资料" : "Manage patent documents and materials",
      color: "bg-orange-500"
    },
    {
      title: locale === "zh" ? "AI 助手" : "AI Assistant",
      url: "/ai-assistant",
      icon: "RiRobot2Line",
      description: locale === "zh" ? "智能专利助手服务" : "Intelligent patent assistant service",
      color: "bg-pink-500"
    },
    {
      title: locale === "zh" ? "设置" : "Settings",
      url: "/settings",
      icon: "RiSettingsLine",
      description: locale === "zh" ? "个人设置和偏好配置" : "Personal settings and preferences",
      color: "bg-gray-500"
    }
  ];

  return (
    <div className="flex min-h-screen w-full">
      {/* 侧边栏 */}
      <div className="w-16 bg-sidebar border-r border-border flex flex-col items-center py-4 space-y-2">
        {/* Logo */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex size-10 items-center justify-center rounded-lg bg-primary text-primary-foreground mb-4 cursor-pointer hover:scale-105 transition-transform">
                <Icon name="RiFileTextLine" className="size-5" />
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p className="font-medium">
                {locale === "zh" ? "专利助手" : "Patent Assistant"}
              </p>
              <p className="text-xs text-muted-foreground">
                {locale === "zh" ? "智能管理平台" : "Smart Platform"}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Separator className="w-8" />

        {/* 导航菜单 */}
        <div className="flex flex-col space-y-2 flex-1">
          {sidebarItems.map((item, index) => (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={item.url}
                    className="group flex size-10 items-center justify-center rounded-lg hover:bg-accent transition-colors relative"
                  >
                    <div className={`flex size-8 items-center justify-center rounded-md ${item.color} text-white group-hover:scale-110 transition-transform shadow-sm`}>
                      <Icon name={item.icon} className="size-4" />
                    </div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="max-w-xs">
                  <p className="font-medium">{item.title}</p>
                  <p className="text-xs text-muted-foreground">{item.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>

        {/* 底部工具 */}
        <div className="flex flex-col space-y-2 mt-auto">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="size-10">
                  <Icon name="RiTranslate2" className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{locale === "zh" ? "语言设置" : "Language"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="size-10">
                  <Icon name="RiMoonLine" className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{locale === "zh" ? "主题切换" : "Theme"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="size-10">
                  <Icon name="RiFullscreenLine" className="size-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{locale === "zh" ? "全屏" : "Fullscreen"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex size-10 items-center justify-center rounded-lg bg-primary text-primary-foreground cursor-pointer hover:scale-105 transition-transform">
                  <span className="text-sm font-semibold">D</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p className="font-medium">{locale === "zh" ? "用户" : "User"}</p>
                <p className="text-xs text-muted-foreground">{locale === "zh" ? "在线" : "Online"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部栏 */}
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <div className="flex-1">
            <h1 className="text-lg font-semibold">
              {locale === "zh" ? "欢迎使用专利助手" : "Welcome to Patent Assistant"}
            </h1>
            <p className="text-sm text-muted-foreground">
              {locale === "zh"
                ? "选择功能开始您的专利管理之旅"
                : "Choose a function to start your patent management journey"
              }
            </p>
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/" className="flex items-center gap-2">
              <Icon name="RiHomeLine" className="size-4" />
              {locale === "zh" ? "返回首页" : "Home"}
            </Link>
          </Button>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4">
          {/* 快速开始卡片 */}
          <div className="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-3">
            {sidebarItems.slice(0, 3).map((item, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer group">
                <Link href={item.url}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-3">
                      <div className={`flex size-10 items-center justify-center rounded-lg ${item.color} text-white group-hover:scale-110 transition-transform`}>
                        <Icon name={item.icon} className="size-5" />
                      </div>
                      <div>
                        <CardTitle className="text-base">{item.title}</CardTitle>
                        <Badge variant="secondary" className="text-xs">
                          {locale === "zh" ? "快速开始" : "Quick Start"}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {item.description}
                    </CardDescription>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>

          {/* 功能概览 */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon name="RiSparklingLine" className="size-5 text-primary" />
                  {locale === "zh" ? "核心功能" : "Core Features"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {sidebarItems.map((item, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className={`flex size-8 items-center justify-center rounded-md ${item.color} text-white`}>
                      <Icon name={item.icon} className="size-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">{item.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {locale === "zh" ? "可用" : "Available"}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {locale === "zh" ? "就绪" : "Ready"}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon name="RiTimeLine" className="size-5 text-primary" />
                  {locale === "zh" ? "最近活动" : "Recent Activity"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                  <div className="flex size-8 items-center justify-center rounded-md bg-green-500 text-white">
                    <Icon name="RiCheckLine" className="size-4" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">
                      {locale === "zh" ? "系统初始化完成" : "System initialized"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {locale === "zh" ? "所有功能模块已就绪" : "All modules are ready"}
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {locale === "zh" ? "刚刚" : "Just now"}
                  </span>
                </div>
                
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/30 transition-colors">
                  <div className="flex size-8 items-center justify-center rounded-md bg-blue-500 text-white">
                    <Icon name="RiUserLine" className="size-4" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">
                      {locale === "zh" ? "欢迎新用户" : "Welcome new user"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {locale === "zh" ? "开始探索专利助手功能" : "Start exploring features"}
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {locale === "zh" ? "1分钟前" : "1m ago"}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 帮助提示 */}
          <Card className="border-dashed">
            <CardContent className="flex items-center gap-4 pt-6">
              <div className="flex size-12 items-center justify-center rounded-full bg-primary/10">
                <Icon name="RiLightbulbLine" className="size-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold">
                  {locale === "zh" ? "开始使用提示" : "Getting Started Tip"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {locale === "zh"
                    ? "点击左侧侧边栏的任意功能图标开始使用，或者点击上方的快速开始卡片。"
                    : "Click any function icon in the sidebar or the quick start cards above to begin."
                  }
                </p>
              </div>
              <Button variant="outline" size="sm">
                {locale === "zh" ? "了解更多" : "Learn More"}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default async function StartPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  return <StartPageClient locale={locale} />;
}
