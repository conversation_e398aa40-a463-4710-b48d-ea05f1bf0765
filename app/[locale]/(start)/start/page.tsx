import { getTranslations } from "next-intl/server";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";

export default async function StartPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  const sidebarItems = [
    {
      title: locale === "zh" ? "仪表盘" : "Dashboard",
      url: "/dashboard",
      icon: "RiDashboardLine",
      description: locale === "zh" ? "查看项目概览和统计数据" : "View project overview and statistics"
    },
    {
      title: locale === "zh" ? "专利搜索" : "Patent Search",
      url: "/patent-search",
      icon: "RiSearchLine",
      description: locale === "zh" ? "搜索和分析专利信息" : "Search and analyze patent information"
    },
    {
      title: locale === "zh" ? "专利分析" : "Patent Analysis",
      url: "/patent-analysis",
      icon: "RiBarChartLine",
      description: locale === "zh" ? "深度分析专利数据" : "Deep analysis of patent data"
    },
    {
      title: locale === "zh" ? "文档管理" : "Document Management",
      url: "/documents",
      icon: "RiFileTextLine",
      description: locale === "zh" ? "管理专利文档和资料" : "Manage patent documents and materials"
    },
    {
      title: locale === "zh" ? "AI 助手" : "AI Assistant",
      url: "/ai-assistant",
      icon: "RiRobot2Line",
      description: locale === "zh" ? "智能专利助手服务" : "Intelligent patent assistant service"
    },
    {
      title: locale === "zh" ? "设置" : "Settings",
      url: "/settings",
      icon: "RiSettingsLine",
      description: locale === "zh" ? "个人设置和偏好配置" : "Personal settings and preferences"
    }
  ];

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* 左侧边栏 */}
      <aside className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Logo 和标题 */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Icon name="RiFileTextLine" className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {locale === "zh" ? "专利助手" : "Patent Assistant"}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {locale === "zh" ? "智能专利管理平台" : "Intelligent Patent Management Platform"}
              </p>
            </div>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {sidebarItems.map((item, index) => (
              <Link
                key={index}
                href={item.url}
                className="group flex items-start gap-3 p-4 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                  <Icon name={item.icon} className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {item.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </nav>

        {/* 底部信息 */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <Icon name="RiUserLine" className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {locale === "zh" ? "用户" : "User"}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {locale === "zh" ? "在线" : "Online"}
              </p>
            </div>
          </div>
        </div>
      </aside>

      {/* 主内容区 */}
      <main className="flex-1 flex flex-col">
        {/* 顶部栏 */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                {locale === "zh" ? "欢迎使用专利助手" : "Welcome to Patent Assistant"}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {locale === "zh"
                  ? "选择左侧功能开始您的专利管理之旅"
                  : "Choose a function from the sidebar to start your patent management journey"
                }
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <Icon name="RiHomeLine" className="w-4 h-4" />
                {locale === "zh" ? "返回首页" : "Back to Home"}
              </Link>
            </div>
          </div>
        </header>

        {/* 内容区域 */}
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto">
            {/* 快速开始卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                  <Icon name="RiSearchLine" className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {locale === "zh" ? "快速搜索" : "Quick Search"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  {locale === "zh"
                    ? "快速搜索专利信息，获取相关数据"
                    : "Quickly search patent information and get relevant data"
                  }
                </p>
                <Link
                  href="/patent-search"
                  className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm"
                >
                  {locale === "zh" ? "开始搜索" : "Start Search"}
                  <Icon name="RiArrowRightLine" className="w-4 h-4" />
                </Link>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
                  <Icon name="RiBarChartLine" className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {locale === "zh" ? "数据分析" : "Data Analysis"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  {locale === "zh"
                    ? "深度分析专利趋势和竞争情况"
                    : "Deep analysis of patent trends and competitive landscape"
                  }
                </p>
                <Link
                  href="/patent-analysis"
                  className="inline-flex items-center gap-2 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium text-sm"
                >
                  {locale === "zh" ? "查看分析" : "View Analysis"}
                  <Icon name="RiArrowRightLine" className="w-4 h-4" />
                </Link>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4">
                  <Icon name="RiRobot2Line" className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {locale === "zh" ? "AI 助手" : "AI Assistant"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  {locale === "zh"
                    ? "智能助手帮您处理专利相关问题"
                    : "AI assistant to help with patent-related questions"
                  }
                </p>
                <Link
                  href="/ai-assistant"
                  className="inline-flex items-center gap-2 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium text-sm"
                >
                  {locale === "zh" ? "开始对话" : "Start Chat"}
                  <Icon name="RiArrowRightLine" className="w-4 h-4" />
                </Link>
              </div>
            </div>

            {/* 最近活动 */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {locale === "zh" ? "最近活动" : "Recent Activity"}
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Icon name="RiSearchLine" className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {locale === "zh" ? "专利搜索功能已就绪" : "Patent search feature is ready"}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {locale === "zh" ? "您可以开始搜索专利信息" : "You can start searching for patent information"}
                    </p>
                  </div>
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    {locale === "zh" ? "刚刚" : "Just now"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
