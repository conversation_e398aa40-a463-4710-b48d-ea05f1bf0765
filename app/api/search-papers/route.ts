import { NextRequest, NextResponse } from 'next/server';
import { semanticScholarAPI } from '@/lib/semantic-scholar';

export async function POST(request: NextRequest) {
  try {
    const { query, limit = 10 } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query parameter is required and must be a string' },
        { status: 400 }
      );
    }

    // 搜索论文
    const searchResults = await semanticScholarAPI.searchPapers(query, limit);
    
    // 处理和格式化结果
    const formattedResults = searchResults.data
      .filter(paper => paper.abstract && paper.abstract.length > 50) // 过滤掉没有摘要或摘要太短的论文
      .map(paper => {
        const relevanceScore = semanticScholarAPI.calculateRelevanceScore(paper, query);
        
        return {
          id: paper.paperId,
          title: paper.title,
          authors: semanticScholarAPI.formatAuthors(paper.authors),
          venue: semanticScholarAPI.formatVenue(paper),
          year: paper.year || 'Unknown',
          abstract: paper.abstract,
          citationCount: paper.citationCount || 0,
          influentialCitationCount: paper.influentialCitationCount || 0,
          url: paper.url,
          fieldsOfStudy: paper.fieldsOfStudy || [],
          relevance: relevanceScore,
          publicationDate: paper.publicationDate
        };
      })
      .sort((a, b) => b.relevance - a.relevance) // 按相关度排序
      .slice(0, limit); // 限制结果数量

    return NextResponse.json({
      success: true,
      total: searchResults.total,
      results: formattedResults
    });

  } catch (error) {
    console.error('Error in search-papers API:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to search papers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST instead.' },
    { status: 405 }
  );
}
