// Semantic Scholar API 服务
export interface Paper {
  paperId: string;
  title: string;
  abstract: string | null;
  authors: Array<{
    name: string;
    authorId?: string;
  }>;
  year: number | null;
  venue: string | null;
  citationCount: number;
  influentialCitationCount: number;
  url: string | null;
  fieldsOfStudy: string[] | null;
  publicationDate: string | null;
}

export interface SearchResponse {
  total: number;
  offset: number;
  next: number | null;
  data: Paper[];
}

class SemanticScholarAPI {
  private baseURL = 'https://api.semanticscholar.org/graph/v1';
  private lastRequestTime = 0;
  private minRequestInterval = 1000; // 最小请求间隔1秒

  // 等待函数
  private async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 速率限制控制
  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      await this.wait(this.minRequestInterval - timeSinceLastRequest);
    }

    this.lastRequestTime = Date.now();
  }

  // 重试机制
  private async fetchWithRetry(
    url: string,
    options: RequestInit,
    maxRetries: number = 3
  ): Promise<Response> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        await this.rateLimit();

        const response = await fetch(url, options);

        if (response.status === 429) {
          // 如果是速率限制，等待更长时间后重试
          const retryAfter = response.headers.get('Retry-After');
          const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : (i + 1) * 2000;
          console.log(`Rate limited, waiting ${waitTime}ms before retry ${i + 1}/${maxRetries}`);
          await this.wait(waitTime);
          continue;
        }

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries) {
          console.log(`Request failed, retrying ${i + 1}/${maxRetries}:`, error);
          await this.wait((i + 1) * 1000); // 递增等待时间
        }
      }
    }

    throw lastError!;
  }

  // 搜索论文
  async searchPapers(
    query: string,
    limit: number = 10,
    offset: number = 0,
    fields: string[] = [
      'paperId',
      'title',
      'abstract',
      'authors',
      'year',
      'venue',
      'citationCount',
      'influentialCitationCount',
      'url',
      'fieldsOfStudy',
      'publicationDate'
    ]
  ): Promise<SearchResponse> {
    try {
      const params = new URLSearchParams({
        query: query,
        limit: Math.min(limit, 10).toString(), // 限制最大10个结果
        offset: offset.toString(),
        fields: fields.join(',')
      });

      const response = await this.fetchWithRetry(
        `${this.baseURL}/paper/search?${params}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Patent-Assistant/1.0 (research purposes)'
          },
        }
      );

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error searching papers:', error);
      throw error;
    }
  }

  // 获取特定论文详情
  async getPaperDetails(paperId: string): Promise<Paper> {
    try {
      const fields = [
        'paperId',
        'title',
        'abstract',
        'authors',
        'year',
        'venue',
        'citationCount',
        'influentialCitationCount',
        'url',
        'fieldsOfStudy',
        'publicationDate'
      ];

      const response = await fetch(
        `${this.baseURL}/paper/${paperId}?fields=${fields.join(',')}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting paper details:', error);
      throw error;
    }
  }

  // 计算相关度分数（基于引用数和影响力引用数）
  calculateRelevanceScore(paper: Paper, query: string): number {
    let score = 0;

    // 基础分数：基于引用数
    if (paper.citationCount > 0) {
      score += Math.min(paper.citationCount / 100, 30); // 最多30分
    }

    // 影响力分数：基于有影响力的引用数
    if (paper.influentialCitationCount > 0) {
      score += Math.min(paper.influentialCitationCount / 10, 20); // 最多20分
    }

    // 时间分数：越新的论文分数越高
    if (paper.year) {
      const currentYear = new Date().getFullYear();
      const yearDiff = currentYear - paper.year;
      if (yearDiff <= 2) score += 20; // 最近2年
      else if (yearDiff <= 5) score += 15; // 最近5年
      else if (yearDiff <= 10) score += 10; // 最近10年
      else score += 5; // 10年以上
    }

    // 标题匹配分数
    const titleWords = paper.title.toLowerCase().split(' ');
    const queryWords = query.toLowerCase().split(' ');
    const matchCount = queryWords.filter(word =>
      titleWords.some(titleWord => titleWord.includes(word))
    ).length;
    score += (matchCount / queryWords.length) * 20; // 最多20分

    // 摘要匹配分数
    if (paper.abstract) {
      const abstractWords = paper.abstract.toLowerCase().split(' ');
      const abstractMatchCount = queryWords.filter(word =>
        abstractWords.some(abstractWord => abstractWord.includes(word))
      ).length;
      score += (abstractMatchCount / queryWords.length) * 10; // 最多10分
    }

    return Math.min(Math.round(score), 100); // 最高100分
  }

  // 格式化作者列表
  formatAuthors(authors: Array<{name: string}>): string {
    if (!authors || authors.length === 0) return 'Unknown Authors';

    if (authors.length === 1) return authors[0].name;
    if (authors.length === 2) return `${authors[0].name}, ${authors[1].name}`;
    if (authors.length <= 5) {
      return authors.map(a => a.name).join(', ');
    }

    // 超过5个作者时显示前3个加"et al."
    return `${authors.slice(0, 3).map(a => a.name).join(', ')}, et al.`;
  }

  // 格式化发表信息
  formatVenue(paper: Paper): string {
    if (paper.venue) return paper.venue;
    if (paper.fieldsOfStudy && paper.fieldsOfStudy.length > 0) {
      return paper.fieldsOfStudy[0];
    }
    return 'Unknown Venue';
  }
}

export const semanticScholarAPI = new SemanticScholarAPI();
